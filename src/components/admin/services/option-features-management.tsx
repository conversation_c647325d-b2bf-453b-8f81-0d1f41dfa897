'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  cost?: number
  discountRate?: number
  totalDiscount?: number
  isIncluded: boolean
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
    service?: {
      id: string
      name: string
      category?: {
        id: string
        name: string
      }
    }
  }
  _count?: {
    orderDetails: number
  }
}

interface OptionFeaturesManagementProps {
  option: ServiceOption
}

interface FeatureFormData {
  name: string
  description: string
  cost: number
  discountRate: number
  totalDiscount: number
  isIncluded: boolean
}

export function OptionFeaturesManagement({ option }: OptionFeaturesManagementProps) {
  return (
    <div className="p-6">
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Features for "{option.name}"
        </h2>
        <p className="text-gray-600">
          Feature management is temporarily under maintenance. Please check back later.
        </p>
      </div>
    </div>
  )
}
